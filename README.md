# Guess It 2 - Statistical Number Prediction

A Go-based program that analyzes sequences of numbers and predicts ranges for the next number using statistical analysis and pattern recognition.

## Overview

This project builds upon statistical knowledge to create a more sophisticated number guessing system. The program reads numbers from standard input and outputs predicted ranges for subsequent numbers based on:

- Statistical analysis (mean, standard deviation, variance)
- Trend analysis (increasing, decreasing, stable patterns)
- Difference analysis between consecutive numbers
- Adaptive confidence intervals

## Problem Statement

Given a sequence of numbers as standard input, the program must:
1. Analyze the statistical properties of the input sequence
2. Identify patterns and trends in the data
3. Predict a reasonable range for the next number
4. Output the range in the format: `min_value max_value`

## Features

- **Real-time Analysis**: Processes numbers as they arrive
- **Multiple Statistical Methods**: Uses various statistical approaches for robust predictions
- **Trend Detection**: Identifies increasing, decreasing, or stable patterns
- **Adaptive Ranges**: Adjusts prediction confidence based on data variability
- **Performance Optimized**: Efficient algorithms for real-time processing

## Input Format

The program reads numbers from standard input, one per line:
```
10
12
15
18
22
```

## Output Format

For each input number (after the first), outputs a predicted range:
```
8.00 16.00
10.00 20.00
12.00 24.00
15.00 29.00
```

## Technical Approach

### Statistical Methods
1. **Basic Statistics**: Mean, median, standard deviation
2. **Trend Analysis**: First-order differences and their statistics
3. **Variance Analysis**: Data spread and consistency measures
4. **Confidence Intervals**: Adaptive range sizing based on data uncertainty

### Algorithm Components
- **Data Collection**: Maintains running sequence of input numbers
- **Difference Calculation**: Tracks changes between consecutive numbers
- **Trend Detection**: Analyzes direction and magnitude of changes
- **Range Prediction**: Combines trend and variance for range estimation

## Project Structure

```
guess-it-2/
├── README.md              # This file
├── ROADMAP.md            # Development roadmap
├── go.mod                # Go module definition
├── main.go               # Main program entry point
├── predictor/            # Core prediction logic
│   ├── predictor.go      # NumberPredictor struct and methods
│   └── stats.go          # Statistical utility functions
├── tests/                # Test files
│   ├── predictor_test.go # Unit tests for predictor
│   └── integration_test.go # Integration tests
├── examples/             # Example usage and demos
│   ├── demo.go           # Interactive demonstration
│   └── benchmark.go      # Performance benchmarks
└── docs/                 # Additional documentation
    ├── algorithm.md      # Detailed algorithm explanation
    └── examples.md       # Usage examples and patterns
```

## Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd guess-it-2
   go mod init guess-it-2
   ```

2. **Build**:
   ```bash
   go build -o guess-it main.go
   ```

3. **Run**:
   ```bash
   echo -e "10\n12\n15\n18\n22" | ./guess-it
   ```

4. **Test**:
   ```bash
   go test ./...
   ```

## Development Status

- [ ] Project initialization and structure
- [ ] Core predictor implementation
- [ ] Statistical utility functions
- [ ] Unit tests
- [ ] Integration tests
- [ ] Performance optimization
- [ ] Documentation and examples
- [ ] Final testing and validation

## Requirements

- Go 1.21 or higher
- Standard library only (no external dependencies)

## Contributing

1. Follow the roadmap in `ROADMAP.md`
2. Write tests for all new functionality
3. Commit frequently with descriptive messages
4. Update documentation as needed

## License

This project is part of a coding challenge/exercise.

## Performance Goals

- Handle sequences of 10,000+ numbers efficiently
- Prediction time: < 1ms per number
- Memory usage: O(n) where n is sequence length
- Accuracy: Reasonable ranges that adapt to data patterns

## Algorithm Notes

The prediction algorithm balances several factors:
- **Trend Continuation**: Assumes recent trends will continue
- **Variance Adaptation**: Wider ranges for more variable data
- **Confidence Scaling**: Adjustable confidence levels
- **Minimum Range**: Ensures predictions aren't too narrow

This approach provides robust predictions across various number patterns including linear sequences, random data, oscillating patterns, and exponential growth.
