#!/bin/bash

# Build script for Guess It 2 - Number Prediction Program

set -e

echo "=== Building Guess It 2 ==="

# Clean previous builds
echo "Cleaning previous builds..."
rm -f guess-it your_program

# Run tests
echo "Running tests..."
go test ./tests/ -v

# Build main program
echo "Building main program..."
go build -o guess-it main.go

# Create symlink with expected name
ln -sf guess-it your_program

echo "Build completed successfully!"
echo ""
echo "Usage:"
echo "  ./guess-it                    # Interactive mode"
echo "  echo '189 113 121' | ./guess-it  # Pipe input"
echo "  ./your_program                # Alternative name"
echo ""
echo "Examples:"
echo "  go run examples/demo.go       # Run demonstration"
echo "  go run examples/benchmark.go  # Run benchmarks"
echo ""
echo "Testing:"
echo "  go test ./tests/              # Run all tests"
echo "  echo -e '189\\n113\\n121\\n114' | ./guess-it  # Test with sample data"
