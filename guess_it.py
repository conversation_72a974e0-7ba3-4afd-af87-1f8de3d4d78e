#!/usr/bin/env python3
"""
Number Guessing Program - Statistical Prediction
Analyzes input numbers and predicts a range for the next number.
"""

import sys
import statistics
import math
from typing import List, Tuple


class NumberPredictor:
    def __init__(self):
        self.numbers = []
        self.differences = []
        
    def add_number(self, num: float) -> None:
        """Add a new number to the sequence."""
        self.numbers.append(num)
        
        # Calculate differences between consecutive numbers
        if len(self.numbers) > 1:
            diff = self.numbers[-1] - self.numbers[-2]
            self.differences.append(diff)
    
    def calculate_basic_stats(self) -> dict:
        """Calculate basic statistical measures."""
        if not self.numbers:
            return {}
            
        return {
            'mean': statistics.mean(self.numbers),
            'median': statistics.median(self.numbers),
            'min': min(self.numbers),
            'max': max(self.numbers),
            'count': len(self.numbers)
        }
    
    def calculate_variance_stats(self) -> dict:
        """Calculate variance and standard deviation."""
        if len(self.numbers) < 2:
            return {'std_dev': 0, 'variance': 0}
            
        return {
            'std_dev': statistics.stdev(self.numbers),
            'variance': statistics.variance(self.numbers)
        }
    
    def analyze_trend(self) -> dict:
        """Analyze trend in the differences."""
        if not self.differences:
            return {'trend': 'none', 'avg_diff': 0}
            
        avg_diff = statistics.mean(self.differences)
        
        if avg_diff > 0:
            trend = 'increasing'
        elif avg_diff < 0:
            trend = 'decreasing'
        else:
            trend = 'stable'
            
        return {
            'trend': trend,
            'avg_diff': avg_diff,
            'diff_std': statistics.stdev(self.differences) if len(self.differences) > 1 else 0
        }
    
    def predict_range(self, confidence_factor: float = 2.0) -> Tuple[float, float]:
        """
        Predict the range for the next number.
        
        Args:
            confidence_factor: Multiplier for standard deviation (higher = wider range)
            
        Returns:
            Tuple of (min_prediction, max_prediction)
        """
        if not self.numbers:
            return (0, 100)  # Default range
            
        if len(self.numbers) == 1:
            # With only one number, make a reasonable guess
            base = self.numbers[0]
            return (base - 50, base + 50)
        
        # Get statistics
        stats = self.calculate_basic_stats()
        var_stats = self.calculate_variance_stats()
        trend_stats = self.analyze_trend()
        
        # Base prediction on the last number plus trend
        last_number = self.numbers[-1]
        predicted_center = last_number + trend_stats['avg_diff']
        
        # Calculate range based on standard deviation and trend variability
        std_dev = var_stats['std_dev']
        diff_std = trend_stats.get('diff_std', std_dev)
        
        # Use the larger of number std dev or difference std dev for range
        range_factor = max(std_dev, diff_std) * confidence_factor
        
        # Ensure minimum range
        if range_factor < 1:
            range_factor = 10
            
        min_pred = predicted_center - range_factor
        max_pred = predicted_center + range_factor
        
        return (min_pred, max_pred)
    
    def get_prediction_info(self) -> dict:
        """Get detailed prediction information."""
        basic_stats = self.calculate_basic_stats()
        var_stats = self.calculate_variance_stats()
        trend_stats = self.analyze_trend()
        min_pred, max_pred = self.predict_range()
        
        return {
            'basic_stats': basic_stats,
            'variance_stats': var_stats,
            'trend_stats': trend_stats,
            'prediction_range': (min_pred, max_pred)
        }


def main():
    """Main program loop."""
    predictor = NumberPredictor()
    
    print("Number Prediction Program")
    print("Enter numbers one by one. The program will predict the range for the next number.")
    print("Enter 'quit' to exit.\n")
    
    try:
        while True:
            try:
                # Read input
                line = input().strip()
                
                if line.lower() in ['quit', 'exit', 'q']:
                    break
                    
                # Try to parse as number
                number = float(line)
                predictor.add_number(number)
                
                # Get prediction for next number
                min_pred, max_pred = predictor.predict_range()
                
                print(f"{min_pred:.2f} {max_pred:.2f}")
                
            except ValueError:
                print("Please enter a valid number or 'quit' to exit.", file=sys.stderr)
            except EOFError:
                break
                
    except KeyboardInterrupt:
        print("\nProgram interrupted.")


if __name__ == "__main__":
    main()
