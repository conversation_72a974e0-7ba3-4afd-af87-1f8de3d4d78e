# Algorithm Documentation

## Overview

The Guess It 2 number prediction algorithm uses statistical analysis and trend detection to predict ranges for future numbers in a sequence. The algorithm is designed to be adaptive, adjusting its predictions based on the characteristics of the input data.

## Core Components

### 1. Data Storage
- **Numbers Array**: Stores all input numbers in sequence
- **Differences Array**: Stores differences between consecutive numbers
- **Real-time Updates**: Both arrays are updated as new numbers arrive

### 2. Statistical Analysis

#### Basic Statistics
- **Mean**: Average of all numbers seen so far
- **Standard Deviation**: Measure of data spread and variability
- **Min/Max**: Range bounds of the dataset
- **Median**: Middle value for additional robustness

#### Trend Analysis
- **Average Difference**: Mean of consecutive number differences
- **Difference Standard Deviation**: Variability in the trend
- **Trend Direction**: Classified as "increasing", "decreasing", or "stable"
- **Consistency Metric**: Reliability measure of the trend (0-1 scale)

### 3. Prediction Algorithm

#### Step 1: Trend Prediction
```
predicted_center = last_number + average_difference
```

#### Step 2: Range Calculation
```
range_factor = max(numbers_std_dev, differences_std_dev) * confidence_factor
min_prediction = predicted_center - range_factor
max_prediction = predicted_center + range_factor
```

#### Step 3: Adaptive Confidence
- **High Consistency** (>0.5): `confidence_factor = 1.5` (narrower range)
- **Medium Consistency**: `confidence_factor = 2.0` (standard range)
- **Low Consistency** (<0.2): `confidence_factor = 3.0` (wider range)

## Algorithm Features

### Adaptive Behavior
1. **Trend-Based**: Predictions follow detected trends in the data
2. **Variance-Aware**: Range width adapts to data volatility
3. **Consistency-Driven**: More reliable trends produce narrower ranges
4. **Minimum Bounds**: Ensures predictions aren't unreasonably narrow

### Edge Case Handling
1. **Empty Data**: Returns default range [0, 200]
2. **Single Number**: Returns range [number-50, number+50]
3. **Stable Data**: Uses minimum range width to avoid zero-width predictions
4. **Volatile Data**: Expands range using percentage-based calculations

### Performance Characteristics
- **Time Complexity**: O(1) per prediction (after initial O(n) for statistics)
- **Space Complexity**: O(n) where n is the sequence length
- **Memory Efficiency**: Stores only essential data points

## Mathematical Foundation

### Trend Consistency
The consistency metric is calculated as:
```
coefficient_of_variation = std_dev_differences / |average_difference|
consistency = 1 / (1 + coefficient_of_variation)
```

This provides a normalized measure (0-1) where:
- 1.0 = perfectly consistent trend
- 0.5 = moderate consistency
- 0.0 = highly inconsistent/random

### Range Sizing
The range factor combines multiple sources of uncertainty:
1. **Data Spread**: Standard deviation of all numbers
2. **Trend Variability**: Standard deviation of differences
3. **Confidence Level**: Based on trend consistency
4. **Minimum Width**: Ensures practical usability

### Volatility Detection
For highly volatile data (std_dev > 30% of mean):
```
range_factor = max(range_factor, |predicted_center| * 0.2)
```

This ensures ranges remain reasonable for percentage-based variations.

## Algorithm Validation

### Test Patterns
The algorithm has been validated against various patterns:

1. **Linear Sequences**: Consistent trend detection and narrow ranges
2. **Quadratic Growth**: Adapts to accelerating changes
3. **Random Data**: Produces appropriately wide ranges
4. **Oscillating Patterns**: Balances trend and variance
5. **Step Functions**: Handles sudden changes gracefully

### Performance Metrics
- **Accuracy**: Percentage of actual values falling within predicted ranges
- **Range Quality**: Balance between accuracy and range width
- **Computational Efficiency**: Processing speed for large datasets
- **Memory Usage**: Linear growth with sequence length

## Implementation Notes

### Numerical Stability
- Uses sample standard deviation (n-1 denominator) for small datasets
- Handles division by zero in consistency calculations
- Ensures min < max in all range predictions

### Optimization Opportunities
1. **Sliding Window**: For very long sequences, maintain only recent N values
2. **Weighted Trends**: Give more importance to recent differences
3. **Pattern Recognition**: Detect and handle specific patterns (seasonal, cyclical)
4. **Multi-Model Ensemble**: Combine multiple prediction strategies

### Limitations
1. **Memory Growth**: Stores entire sequence (can be optimized)
2. **Cold Start**: Requires several data points for reliable predictions
3. **Pattern Assumptions**: Works best with somewhat predictable data
4. **No Seasonality**: Doesn't detect cyclical patterns automatically

## Future Enhancements

### Advanced Features
1. **Exponential Smoothing**: Weight recent data more heavily
2. **Change Point Detection**: Identify when patterns shift
3. **Confidence Intervals**: Provide multiple confidence levels
4. **Pattern Classification**: Automatic detection of sequence types

### Performance Improvements
1. **Incremental Statistics**: Update statistics without full recalculation
2. **Memory Management**: Implement sliding window for long sequences
3. **Parallel Processing**: Utilize multiple cores for large datasets
4. **Caching**: Store intermediate calculations for repeated queries
