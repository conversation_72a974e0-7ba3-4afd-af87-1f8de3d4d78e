package predictor

import "math"

// Mean calculates the arithmetic mean of a slice of numbers
func Mean(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	sum := 0.0
	for _, num := range numbers {
		sum += num
	}
	return sum / float64(len(numbers))
}

// StandardDeviation calculates the sample standard deviation
func StandardDeviation(numbers []float64) float64 {
	if len(numbers) < 2 {
		return 0
	}
	
	mean := Mean(numbers)
	sumSquaredDiffs := 0.0
	
	for _, num := range numbers {
		diff := num - mean
		sumSquaredDiffs += diff * diff
	}
	
	variance := sumSquaredDiffs / float64(len(numbers)-1)
	return math.Sqrt(variance)
}

// Min returns the minimum value in a slice
func Min(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	min := numbers[0]
	for _, num := range numbers[1:] {
		if num < min {
			min = num
		}
	}
	return min
}

// Max returns the maximum value in a slice
func Max(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	max := numbers[0]
	for _, num := range numbers[1:] {
		if num > max {
			max = num
		}
	}
	return max
}

// Median calculates the median of a slice of numbers
func Median(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	
	// Create a copy and sort it
	sorted := make([]float64, len(numbers))
	copy(sorted, numbers)
	
	// Simple bubble sort for small arrays
	for i := 0; i < len(sorted); i++ {
		for j := 0; j < len(sorted)-1-i; j++ {
			if sorted[j] > sorted[j+1] {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}
	
	n := len(sorted)
	if n%2 == 0 {
		return (sorted[n/2-1] + sorted[n/2]) / 2
	}
	return sorted[n/2]
}
