package predictor

import "math"

// NumberPredictor analyzes sequences of numbers and predicts ranges for future values
type NumberPredictor struct {
	numbers     []float64 // All numbers seen so far
	differences []float64 // Differences between consecutive numbers
}

// New creates a new NumberPredictor instance
func New() *NumberPredictor {
	return &NumberPredictor{
		numbers:     make([]float64, 0),
		differences: make([]float64, 0),
	}
}

// AddNumber adds a new number to the sequence and updates internal state
func (np *NumberPredictor) AddNumber(num float64) {
	np.numbers = append(np.numbers, num)

	// Calculate difference from previous number
	if len(np.numbers) > 1 {
		diff := np.numbers[len(np.numbers)-1] - np.numbers[len(np.numbers)-2]
		np.differences = append(np.differences, diff)
	}
}

// TrendInfo contains information about the trend in the data
type TrendInfo struct {
	Direction   string  // "increasing", "decreasing", "stable"
	AvgDiff     float64 // Average difference between consecutive numbers
	DiffStdDev  float64 // Standard deviation of differences
	Consistency float64 // How consistent the trend is (0-1)
}

// AnalyzeTrend analyzes the trend in the sequence
func (np *NumberPredictor) AnalyzeTrend() TrendInfo {
	if len(np.differences) == 0 {
		return TrendInfo{Direction: "none", AvgDiff: 0, DiffStdDev: 0, Consistency: 0}
	}

	avgDiff := Mean(np.differences)
	diffStdDev := StandardDeviation(np.differences)

	// Determine trend direction
	direction := "stable"
	if avgDiff > 0.5 {
		direction = "increasing"
	} else if avgDiff < -0.5 {
		direction = "decreasing"
	}

	// Calculate consistency (inverse of coefficient of variation)
	consistency := 0.0
	if diffStdDev > 0 && math.Abs(avgDiff) > 0 {
		cv := diffStdDev / math.Abs(avgDiff)
		consistency = 1.0 / (1.0 + cv) // Higher consistency = lower variation
	}

	return TrendInfo{
		Direction:   direction,
		AvgDiff:     avgDiff,
		DiffStdDev:  diffStdDev,
		Consistency: consistency,
	}
}

// PredictRange predicts the range for the next number
func (np *NumberPredictor) PredictRange() (float64, float64) {
	if len(np.numbers) == 0 {
		return 0, 200 // Default range if no data
	}

	if len(np.numbers) == 1 {
		// With only one number, create a reasonable range around it
		base := np.numbers[0]
		return base - 50, base + 50
	}

	lastNumber := np.numbers[len(np.numbers)-1]
	trend := np.AnalyzeTrend()

	// Base prediction on last number plus trend
	predictedCenter := lastNumber + trend.AvgDiff

	// Calculate range based on various factors
	numbersStdDev := StandardDeviation(np.numbers)

	// Use trend consistency to adjust confidence
	// More consistent trends = narrower ranges
	// Less consistent trends = wider ranges
	confidenceFactor := 2.0
	if trend.Consistency > 0.5 {
		confidenceFactor = 1.5 // More confident, narrower range
	} else if trend.Consistency < 0.2 {
		confidenceFactor = 3.0 // Less confident, wider range
	}

	// Use the larger of number std dev or difference std dev
	rangeFactor := math.Max(numbersStdDev, trend.DiffStdDev) * confidenceFactor

	// Ensure minimum range width
	if rangeFactor < 10 {
		rangeFactor = 10
	}

	// For very volatile data, use a percentage-based range
	if numbersStdDev > Mean(np.numbers)*0.3 {
		rangeFactor = math.Max(rangeFactor, math.Abs(predictedCenter)*0.2)
	}

	minPred := predictedCenter - rangeFactor
	maxPred := predictedCenter + rangeFactor

	// Ensure ranges make sense (min < max)
	if minPred >= maxPred {
		minPred = predictedCenter - 20
		maxPred = predictedCenter + 20
	}

	return minPred, maxPred
}

// GetStats returns comprehensive statistics about the current sequence
func (np *NumberPredictor) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if len(np.numbers) > 0 {
		stats["count"] = len(np.numbers)
		stats["mean"] = Mean(np.numbers)
		stats["std_dev"] = StandardDeviation(np.numbers)
		stats["min"] = Min(np.numbers)
		stats["max"] = Max(np.numbers)
		stats["last"] = np.numbers[len(np.numbers)-1]

		if len(np.numbers) > 1 {
			stats["median"] = Median(np.numbers)
		}
	}

	trend := np.AnalyzeTrend()
	stats["trend"] = trend

	return stats
}
