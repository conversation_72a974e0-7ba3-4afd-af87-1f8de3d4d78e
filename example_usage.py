#!/usr/bin/env python3
"""
Example usage of the number guessing program.
Shows how to use it programmatically and via command line.
"""

from guess_it import NumberPredictor


def example_programmatic_usage():
    """Example of using the NumberPredictor class programmatically."""
    print("=== Programmatic Usage Example ===")
    
    predictor = NumberPredictor()
    
    # Simulate a sequence of numbers
    sequence = [10, 12, 15, 18, 22, 25]
    
    print("Analyzing sequence:", sequence)
    print()
    
    for i, number in enumerate(sequence):
        predictor.add_number(number)
        
        if i > 0:  # Skip first number since we need at least 2 for prediction
            min_pred, max_pred = predictor.predict_range()
            print(f"After seeing {sequence[:i+1]}")
            print(f"  Predicted range for next number: [{min_pred:.2f}, {max_pred:.2f}]")
            
            # If we have the actual next number, show if it was in range
            if i < len(sequence) - 1:
                actual_next = sequence[i + 1]
                in_range = min_pred <= actual_next <= max_pred
                print(f"  Actual next number: {actual_next} ({'✓' if in_range else '✗'} in range)")
            print()


def example_command_line_usage():
    """Show example of command line usage."""
    print("=== Command Line Usage Example ===")
    print("To use the program from command line:")
    print("python3 guess_it.py")
    print()
    print("Then enter numbers one by one:")
    print("10")
    print("12")
    print("15")
    print("18")
    print("quit")
    print()
    print("The program will output predicted ranges after each number.")


def analyze_different_patterns():
    """Analyze different types of number patterns."""
    print("=== Pattern Analysis Examples ===")
    
    patterns = {
        "Linear increasing": [1, 2, 3, 4, 5, 6],
        "Linear decreasing": [20, 18, 16, 14, 12, 10],
        "Quadratic": [1, 4, 9, 16, 25, 36],
        "Random-like": [5, 12, 8, 15, 3, 18, 7],
        "Oscillating": [10, 5, 15, 3, 20, 1, 25],
        "Exponential-like": [2, 4, 8, 16, 32, 64]
    }
    
    for pattern_name, sequence in patterns.items():
        print(f"\n{pattern_name}: {sequence}")
        
        predictor = NumberPredictor()
        for num in sequence:
            predictor.add_number(num)
        
        min_pred, max_pred = predictor.predict_range()
        info = predictor.get_prediction_info()
        
        print(f"  Final prediction range: [{min_pred:.2f}, {max_pred:.2f}]")
        print(f"  Trend: {info['trend_stats']['trend']}")
        print(f"  Average difference: {info['trend_stats']['avg_diff']:.2f}")


if __name__ == "__main__":
    example_programmatic_usage()
    print("\n" + "="*60 + "\n")
    example_command_line_usage()
    print("\n" + "="*60 + "\n")
    analyze_different_patterns()
