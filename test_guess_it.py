#!/usr/bin/env python3
"""
Test script for the number guessing program.
"""

import subprocess
import sys
from io import String<PERSON>


def test_with_input(input_numbers):
    """Test the program with a sequence of input numbers."""
    print(f"Testing with input: {input_numbers}")
    
    # Create input string
    input_str = '\n'.join(map(str, input_numbers)) + '\nquit\n'
    
    # Run the program
    try:
        result = subprocess.run(
            [sys.executable, 'guess_it.py'],
            input=input_str,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        print("Output:")
        print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Program timed out")
        return False
    except Exception as e:
        print(f"Error running program: {e}")
        return False


def test_predictor_class():
    """Test the NumberPredictor class directly."""
    from guess_it import NumberPredictor
    
    print("Testing NumberPredictor class...")
    
    predictor = NumberPredictor()
    
    # Test with simple sequence
    test_sequence = [1, 2, 3, 4, 5]
    
    for num in test_sequence:
        predictor.add_number(num)
        min_pred, max_pred = predictor.predict_range()
        print(f"After adding {num}: predicted range [{min_pred:.2f}, {max_pred:.2f}]")
    
    # Test with random sequence
    print("\nTesting with random-like sequence...")
    predictor2 = NumberPredictor()
    random_sequence = [10, 15, 12, 18, 14, 20]
    
    for num in random_sequence:
        predictor2.add_number(num)
        min_pred, max_pred = predictor2.predict_range()
        print(f"After adding {num}: predicted range [{min_pred:.2f}, {max_pred:.2f}]")
    
    # Show detailed info
    print("\nDetailed prediction info:")
    info = predictor2.get_prediction_info()
    for key, value in info.items():
        print(f"{key}: {value}")


def main():
    """Run all tests."""
    print("=== Testing NumberPredictor Class ===")
    test_predictor_class()
    
    print("\n=== Testing Program with Input ===")
    
    # Test case 1: Simple increasing sequence
    test_with_input([1, 2, 3, 4])
    
    print("\n" + "="*50)
    
    # Test case 2: Random numbers
    test_with_input([10, 15, 12, 18, 14])
    
    print("\n" + "="*50)
    
    # Test case 3: Decreasing sequence
    test_with_input([100, 90, 80, 70])


if __name__ == "__main__":
    main()
