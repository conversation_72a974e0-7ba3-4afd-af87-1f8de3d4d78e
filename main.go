package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"
	
	"guess-it-2/predictor"
)

func main() {
	pred := predictor.New()
	scanner := bufio.NewScanner(os.Stdin)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		
		// Skip empty lines
		if line == "" {
			continue
		}
		
		// Parse the input number
		number, err := strconv.ParseFloat(line, 64)
		if err != nil {
			// Skip invalid input silently (as per typical contest requirements)
			continue
		}
		
		// Add the number to our predictor
		pred.AddNumber(number)
		
		// Predict range for the next number
		min, max := pred.PredictRange()
		
		// Output the predicted range
		fmt.Printf("%.0f %.0f\n", min, max)
	}
	
	// Handle scanner errors
	if err := scanner.Err(); err != nil {
		fmt.Fprintf(os.Stderr, "Error reading input: %v\n", err)
		os.Exit(1)
	}
}
