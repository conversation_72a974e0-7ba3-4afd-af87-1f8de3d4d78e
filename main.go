package main

import (
	"bufio"
	"fmt"
	"math"
	"os"
	"strconv"
	"strings"
)

// NumberPredictor handles the statistical analysis and prediction
type NumberPredictor struct {
	numbers     []float64
	differences []float64
}

// NewNumberPredictor creates a new predictor instance
func NewNumberPredictor() *NumberPredictor {
	return &NumberPredictor{
		numbers:     make([]float64, 0),
		differences: make([]float64, 0),
	}
}

// AddNumber adds a new number to the sequence
func (np *NumberPredictor) AddNumber(num float64) {
	np.numbers = append(np.numbers, num)
	
	// Calculate differences between consecutive numbers
	if len(np.numbers) > 1 {
		diff := np.numbers[len(np.numbers)-1] - np.numbers[len(np.numbers)-2]
		np.differences = append(np.differences, diff)
	}
}

// Mean calculates the mean of a slice of numbers
func mean(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	sum := 0.0
	for _, num := range numbers {
		sum += num
	}
	return sum / float64(len(numbers))
}

// StandardDeviation calculates the standard deviation
func standardDeviation(numbers []float64) float64 {
	if len(numbers) < 2 {
		return 0
	}
	
	m := mean(numbers)
	sumSquaredDiffs := 0.0
	
	for _, num := range numbers {
		diff := num - m
		sumSquaredDiffs += diff * diff
	}
	
	variance := sumSquaredDiffs / float64(len(numbers)-1)
	return math.Sqrt(variance)
}

// AnalyzeTrend analyzes the trend in the differences
func (np *NumberPredictor) AnalyzeTrend() (string, float64, float64) {
	if len(np.differences) == 0 {
		return "none", 0, 0
	}
	
	avgDiff := mean(np.differences)
	diffStd := standardDeviation(np.differences)
	
	trend := "stable"
	if avgDiff > 0 {
		trend = "increasing"
	} else if avgDiff < 0 {
		trend = "decreasing"
	}
	
	return trend, avgDiff, diffStd
}

// PredictRange predicts the range for the next number
func (np *NumberPredictor) PredictRange(confidenceFactor float64) (float64, float64) {
	if len(np.numbers) == 0 {
		return 0, 100 // Default range
	}
	
	if len(np.numbers) == 1 {
		// With only one number, make a reasonable guess
		base := np.numbers[0]
		return base - 50, base + 50
	}
	
	// Get statistics
	_, avgDiff, diffStd := np.AnalyzeTrend()
	numbersStd := standardDeviation(np.numbers)
	
	// Base prediction on the last number plus trend
	lastNumber := np.numbers[len(np.numbers)-1]
	predictedCenter := lastNumber + avgDiff
	
	// Calculate range based on standard deviation and trend variability
	rangeFactor := math.Max(numbersStd, diffStd) * confidenceFactor
	
	// Ensure minimum range
	if rangeFactor < 1 {
		rangeFactor = 10
	}
	
	minPred := predictedCenter - rangeFactor
	maxPred := predictedCenter + rangeFactor
	
	return minPred, maxPred
}

// GetStats returns detailed statistics
func (np *NumberPredictor) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	if len(np.numbers) > 0 {
		stats["count"] = len(np.numbers)
		stats["mean"] = mean(np.numbers)
		stats["std_dev"] = standardDeviation(np.numbers)
		stats["last_number"] = np.numbers[len(np.numbers)-1]
		
		if len(np.numbers) >= 2 {
			stats["min"] = min(np.numbers)
			stats["max"] = max(np.numbers)
		}
	}
	
	trend, avgDiff, diffStd := np.AnalyzeTrend()
	stats["trend"] = trend
	stats["avg_diff"] = avgDiff
	stats["diff_std"] = diffStd
	
	return stats
}

// Helper function to find minimum in slice
func min(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	minVal := numbers[0]
	for _, num := range numbers[1:] {
		if num < minVal {
			minVal = num
		}
	}
	return minVal
}

// Helper function to find maximum in slice
func max(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0
	}
	maxVal := numbers[0]
	for _, num := range numbers[1:] {
		if num > maxVal {
			maxVal = num
		}
	}
	return maxVal
}

func main() {
	predictor := NewNumberPredictor()
	scanner := bufio.NewScanner(os.Stdin)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		
		// Check for exit conditions
		if line == "quit" || line == "exit" || line == "q" {
			break
		}
		
		// Try to parse as number
		number, err := strconv.ParseFloat(line, 64)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Invalid number: %s\n", line)
			continue
		}
		
		// Add number to predictor
		predictor.AddNumber(number)
		
		// Get prediction for next number
		minPred, maxPred := predictor.PredictRange(2.0)
		
		// Output the predicted range
		fmt.Printf("%.2f %.2f\n", minPred, maxPred)
	}
	
	if err := scanner.Err(); err != nil {
		fmt.Fprintf(os.Stderr, "Error reading input: %v\n", err)
	}
}
