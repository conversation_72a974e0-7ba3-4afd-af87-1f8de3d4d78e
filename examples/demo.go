// Demo program showing number prediction capabilities
// Run with: go run examples/demo.go
package main

import (
	"fmt"
	"strings"

	"guess-it-2/predictor"
)

func main() {
	fmt.Println("=== Guess It 2 - Number Prediction Demo ===\n")

	// Demo with the example sequence from the problem
	fmt.Println("1. Example from Problem Description:")
	fmt.Println("Input sequence: 189, 113, 121, 114, 145, 110, 145")
	fmt.Println()

	exampleSequence := []float64{189, 113, 121, 114, 145, 110, 145}
	pred := predictor.New()

	for i, num := range exampleSequence {
		pred.AddNumber(num)
		min, max := pred.PredictRange()

		fmt.Printf("Input: %.0f", num)
		if i < len(exampleSequence)-1 {
			actual := exampleSequence[i+1]
			inRange := min <= actual && actual <= max
			status := "✗"
			if inRange {
				status = "✓"
			}
			fmt.Printf(" → Range: [%.0f, %.0f] → Next: %.0f %s", min, max, actual, status)
		} else {
			fmt.Printf(" → Range: [%.0f, %.0f]", min, max)
		}
		fmt.Println()
	}

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// Demo with different patterns
	patterns := map[string][]float64{
		"Linear Increasing": {1, 2, 3, 4, 5, 6},
		"Linear Decreasing": {20, 18, 16, 14, 12, 10},
		"Quadratic Growth":  {1, 4, 9, 16, 25, 36},
		"Random-like":       {5, 12, 8, 15, 3, 18, 7},
		"Oscillating":       {10, 5, 15, 3, 20, 1, 25},
	}

	for name, sequence := range patterns {
		fmt.Printf("2. Pattern: %s\n", name)
		fmt.Printf("Sequence: %v\n", sequence)

		pred := predictor.New()
		correctPredictions := 0
		totalPredictions := 0

		for i, num := range sequence {
			pred.AddNumber(num)
			min, max := pred.PredictRange()

			if i < len(sequence)-1 {
				actual := sequence[i+1]
				inRange := min <= actual && actual <= max
				if inRange {
					correctPredictions++
				}
				totalPredictions++

				status := "✗"
				if inRange {
					status = "✓"
				}
				fmt.Printf("  %.0f → [%.1f, %.1f] → %.0f %s\n", num, min, max, actual, status)
			} else {
				fmt.Printf("  %.0f → [%.1f, %.1f]\n", num, min, max)
			}
		}

		if totalPredictions > 0 {
			accuracy := float64(correctPredictions) / float64(totalPredictions) * 100
			fmt.Printf("  Accuracy: %d/%d (%.1f%%)\n", correctPredictions, totalPredictions, accuracy)
		}

		// Show final statistics
		stats := pred.GetStats()
		trend := stats["trend"].(predictor.TrendInfo)
		fmt.Printf("  Final stats: trend=%s, avg_diff=%.2f, consistency=%.2f\n",
			trend.Direction, trend.AvgDiff, trend.Consistency)

		fmt.Println()
	}

	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("\n3. Algorithm Explanation:")
	fmt.Println("The prediction algorithm uses several statistical methods:")
	fmt.Println("• Trend Analysis: Calculates average difference between consecutive numbers")
	fmt.Println("• Variance Analysis: Uses standard deviation to determine range width")
	fmt.Println("• Consistency Metrics: Adjusts confidence based on trend reliability")
	fmt.Println("• Adaptive Ranges: Wider ranges for volatile data, narrower for stable trends")
	fmt.Println("\nThe predicted range is: [last_number + trend ± confidence_factor * variance]")

	fmt.Println("\n4. Usage:")
	fmt.Println("To use the main program:")
	fmt.Println("  echo -e '189\\n113\\n121\\n114' | go run main.go")
	fmt.Println("Or build and run:")
	fmt.Println("  go build -o guess-it main.go")
	fmt.Println("  echo -e '189\\n113\\n121\\n114' | ./guess-it")
}
