// Benchmark program for performance testing
// Run with: go run examples/benchmark.go
package main

import (
	"fmt"
	"math"
	"math/rand"
	"time"

	"guess-it-2/predictor"
)

func main() {
	fmt.Println("=== Performance Benchmark ===\n")

	// Test with different sequence sizes
	sizes := []int{100, 1000, 10000, 50000}

	for _, size := range sizes {
		fmt.Printf("Testing with %d numbers...\n", size)

		// Generate test data
		numbers := generateTestSequence(size)

		// Measure performance
		start := time.Now()
		pred := predictor.New()

		for _, num := range numbers {
			pred.AddNumber(num)
			pred.PredictRange() // Include prediction time
		}

		duration := time.Since(start)

		// Calculate metrics
		avgTimePerNumber := duration / time.Duration(size)
		numbersPerSecond := float64(size) / duration.Seconds()

		fmt.Printf("  Total time: %v\n", duration)
		fmt.Printf("  Avg time per number: %v\n", avgTimePerNumber)
		fmt.Printf("  Numbers per second: %.0f\n", numbersPerSecond)

		// Memory usage estimation
		stats := pred.GetStats()
		fmt.Printf("  Final sequence length: %v\n", stats["count"])
		fmt.Printf("  Memory usage (approx): %.2f KB\n", float64(size)*16/1024) // 16 bytes per float64

		fmt.Println()
	}

	// Test prediction accuracy with known patterns
	fmt.Println("=== Accuracy Benchmark ===\n")

	testPatterns := map[string]func(int) []float64{
		"Linear": func(n int) []float64 {
			seq := make([]float64, n)
			for i := 0; i < n; i++ {
				seq[i] = float64(i)*2 + 10
			}
			return seq
		},
		"Quadratic": func(n int) []float64 {
			seq := make([]float64, n)
			for i := 0; i < n; i++ {
				seq[i] = float64(i*i) + 5
			}
			return seq
		},
		"Sine Wave": func(n int) []float64 {
			seq := make([]float64, n)
			for i := 0; i < n; i++ {
				seq[i] = 50 + 30*math.Sin(float64(i)*0.1)
			}
			return seq
		},
		"Random Walk": func(n int) []float64 {
			seq := make([]float64, n)
			seq[0] = 100
			for i := 1; i < n; i++ {
				change := (rand.Float64() - 0.5) * 10
				seq[i] = seq[i-1] + change
			}
			return seq
		},
	}

	for name, generator := range testPatterns {
		fmt.Printf("Pattern: %s\n", name)

		sequence := generator(100)
		pred := predictor.New()

		correct := 0
		total := 0

		for i, num := range sequence {
			pred.AddNumber(num)

			if i < len(sequence)-1 {
				min, max := pred.PredictRange()
				actual := sequence[i+1]

				if min <= actual && actual <= max {
					correct++
				}
				total++
			}
		}

		accuracy := float64(correct) / float64(total) * 100
		fmt.Printf("  Accuracy: %d/%d (%.1f%%)\n", correct, total, accuracy)

		// Show final statistics
		stats := pred.GetStats()
		trend := stats["trend"].(predictor.TrendInfo)
		fmt.Printf("  Trend: %s (avg_diff=%.2f, consistency=%.2f)\n",
			trend.Direction, trend.AvgDiff, trend.Consistency)
		fmt.Println()
	}

	fmt.Println("=== Memory Efficiency Test ===\n")

	// Test memory usage with very long sequences
	fmt.Println("Testing memory efficiency with 100,000 numbers...")

	pred := predictor.New()
	start := time.Now()

	for i := 0; i < 100000; i++ {
		// Simulate realistic data
		value := 100 + float64(i)*0.1 + (rand.Float64()-0.5)*20
		pred.AddNumber(value)

		// Periodically check performance
		if i%10000 == 0 && i > 0 {
			elapsed := time.Since(start)
			rate := float64(i) / elapsed.Seconds()
			fmt.Printf("  Processed %d numbers in %v (%.0f nums/sec)\n", i, elapsed, rate)
		}
	}

	finalTime := time.Since(start)
	fmt.Printf("  Final: 100,000 numbers in %v\n", finalTime)
	fmt.Printf("  Average rate: %.0f numbers/second\n", 100000.0/finalTime.Seconds())
}

func generateTestSequence(size int) []float64 {
	rand.Seed(time.Now().UnixNano())
	numbers := make([]float64, size)

	// Generate a mixed pattern: trend + noise
	base := 100.0
	trend := 0.5

	for i := 0; i < size; i++ {
		noise := (rand.Float64() - 0.5) * 20
		numbers[i] = base + float64(i)*trend + noise
	}

	return numbers
}
