package tests

import (
	"math"
	"testing"

	"guess-it-2/predictor"
)

func TestMean(t *testing.T) {
	tests := []struct {
		name     string
		numbers  []float64
		expected float64
	}{
		{"empty slice", []float64{}, 0},
		{"single number", []float64{5}, 5},
		{"multiple numbers", []float64{1, 2, 3, 4, 5}, 3},
		{"negative numbers", []float64{-1, -2, -3}, -2},
		{"mixed numbers", []float64{-10, 0, 10}, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := predictor.Mean(tt.numbers)
			if result != tt.expected {
				t.Errorf("Mean(%v) = %f, want %f", tt.numbers, result, tt.expected)
			}
		})
	}
}

func TestStandardDeviation(t *testing.T) {
	tests := []struct {
		name      string
		numbers   []float64
		expected  float64
		tolerance float64
	}{
		{"empty slice", []float64{}, 0, 0},
		{"single number", []float64{5}, 0, 0},
		{"identical numbers", []float64{3, 3, 3, 3}, 0, 0},
		{"simple sequence", []float64{1, 2, 3, 4, 5}, 1.5811, 0.001},
		{"larger variance", []float64{1, 10, 1, 10}, 5.1962, 0.001},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := predictor.StandardDeviation(tt.numbers)
			if math.Abs(result-tt.expected) > tt.tolerance {
				t.Errorf("StandardDeviation(%v) = %f, want %f (±%f)",
					tt.numbers, result, tt.expected, tt.tolerance)
			}
		})
	}
}

func TestMinMax(t *testing.T) {
	tests := []struct {
		name        string
		numbers     []float64
		expectedMin float64
		expectedMax float64
	}{
		{"empty slice", []float64{}, 0, 0},
		{"single number", []float64{42}, 42, 42},
		{"positive numbers", []float64{3, 1, 4, 1, 5, 9}, 1, 9},
		{"negative numbers", []float64{-5, -1, -10, -3}, -10, -1},
		{"mixed numbers", []float64{-5, 0, 10, -2, 7}, -5, 10},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			min := predictor.Min(tt.numbers)
			max := predictor.Max(tt.numbers)

			if min != tt.expectedMin {
				t.Errorf("Min(%v) = %f, want %f", tt.numbers, min, tt.expectedMin)
			}
			if max != tt.expectedMax {
				t.Errorf("Max(%v) = %f, want %f", tt.numbers, max, tt.expectedMax)
			}
		})
	}
}

func TestMedian(t *testing.T) {
	tests := []struct {
		name     string
		numbers  []float64
		expected float64
	}{
		{"empty slice", []float64{}, 0},
		{"single number", []float64{5}, 5},
		{"odd count", []float64{1, 3, 2}, 2},
		{"even count", []float64{1, 2, 3, 4}, 2.5},
		{"unsorted", []float64{5, 1, 3, 9, 2}, 3},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := predictor.Median(tt.numbers)
			if result != tt.expected {
				t.Errorf("Median(%v) = %f, want %f", tt.numbers, result, tt.expected)
			}
		})
	}
}
