package tests

import (
	"testing"
	
	"guess-it-2/predictor"
)

func TestNumberPredictor_AddNumber(t *testing.T) {
	pred := predictor.New()
	
	// Test adding first number
	pred.AddNumber(10)
	stats := pred.GetStats()
	if stats["count"] != 1 {
		t.<PERSON><PERSON><PERSON>("Expected count 1, got %v", stats["count"])
	}
	if stats["last"] != 10.0 {
		t.<PERSON><PERSON><PERSON>("Expected last number 10, got %v", stats["last"])
	}
	
	// Test adding second number
	pred.AddNumber(15)
	stats = pred.GetStats()
	if stats["count"] != 2 {
		t.<PERSON><PERSON><PERSON>("Expected count 2, got %v", stats["count"])
	}
	if stats["last"] != 15.0 {
		t.<PERSON><PERSON><PERSON>("Expected last number 15, got %v", stats["last"])
	}
	
	// Check trend analysis
	trend := stats["trend"].(predictor.TrendInfo)
	if trend.Direction != "increasing" {
		t.<PERSON><PERSON><PERSON>("Expected increasing trend, got %s", trend.Direction)
	}
	if trend.AvgDiff != 5.0 {
		t.<PERSON><PERSON><PERSON>("Expected average difference 5, got %f", trend.AvgDiff)
	}
}

func TestAnalyzeTrend(t *testing.T) {
	tests := []struct {
		name              string
		numbers           []float64
		expectedDirection string
		expectedAvgDiff   float64
	}{
		{
			name:              "increasing sequence",
			numbers:           []float64{1, 2, 3, 4, 5},
			expectedDirection: "increasing",
			expectedAvgDiff:   1.0,
		},
		{
			name:              "decreasing sequence", 
			numbers:           []float64{10, 8, 6, 4},
			expectedDirection: "decreasing",
			expectedAvgDiff:   -2.0,
		},
		{
			name:              "stable sequence",
			numbers:           []float64{5, 5, 5, 5},
			expectedDirection: "stable",
			expectedAvgDiff:   0.0,
		},
		{
			name:              "mixed sequence",
			numbers:           []float64{1, 3, 2, 4, 3},
			expectedDirection: "stable", // Small average difference
			expectedAvgDiff:   0.5,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pred := predictor.New()
			for _, num := range tt.numbers {
				pred.AddNumber(num)
			}
			
			trend := pred.AnalyzeTrend()
			if trend.Direction != tt.expectedDirection {
				t.Errorf("Expected direction %s, got %s", tt.expectedDirection, trend.Direction)
			}
			if trend.AvgDiff != tt.expectedAvgDiff {
				t.Errorf("Expected average difference %f, got %f", tt.expectedAvgDiff, trend.AvgDiff)
			}
		})
	}
}

func TestPredictRange(t *testing.T) {
	tests := []struct {
		name     string
		numbers  []float64
		checkFn  func(min, max float64) bool
	}{
		{
			name:    "empty predictor",
			numbers: []float64{},
			checkFn: func(min, max float64) bool {
				return min == 0 && max == 200
			},
		},
		{
			name:    "single number",
			numbers: []float64{100},
			checkFn: func(min, max float64) bool {
				return min == 50 && max == 150
			},
		},
		{
			name:    "increasing sequence",
			numbers: []float64{1, 2, 3, 4},
			checkFn: func(min, max float64) bool {
				// Should predict around 5 (4 + 1)
				return min < 5 && max > 5 && (max-min) > 0
			},
		},
		{
			name:    "decreasing sequence",
			numbers: []float64{10, 8, 6, 4},
			checkFn: func(min, max float64) bool {
				// Should predict around 2 (4 - 2)
				return min < 2 && max > 2 && (max-min) > 0
			},
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pred := predictor.New()
			for _, num := range tt.numbers {
				pred.AddNumber(num)
			}
			
			min, max := pred.PredictRange()
			if !tt.checkFn(min, max) {
				t.Errorf("Range [%f, %f] failed validation for sequence %v", min, max, tt.numbers)
			}
			
			// Basic sanity checks
			if min >= max {
				t.Errorf("Invalid range: min (%f) >= max (%f)", min, max)
			}
		})
	}
}

func TestGetStats(t *testing.T) {
	pred := predictor.New()
	pred.AddNumber(1)
	pred.AddNumber(2)
	pred.AddNumber(3)
	
	stats := pred.GetStats()
	
	// Check basic stats
	if stats["count"] != 3 {
		t.Errorf("Expected count 3, got %v", stats["count"])
	}
	if stats["mean"] != 2.0 {
		t.Errorf("Expected mean 2, got %v", stats["mean"])
	}
	if stats["min"] != 1.0 {
		t.Errorf("Expected min 1, got %v", stats["min"])
	}
	if stats["max"] != 3.0 {
		t.Errorf("Expected max 3, got %v", stats["max"])
	}
	if stats["last"] != 3.0 {
		t.Errorf("Expected last 3, got %v", stats["last"])
	}
	
	// Check trend info
	trend := stats["trend"].(predictor.TrendInfo)
	if trend.Direction != "increasing" {
		t.Errorf("Expected increasing trend, got %s", trend.Direction)
	}
}
