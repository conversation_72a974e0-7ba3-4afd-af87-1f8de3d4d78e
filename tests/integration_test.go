package tests

import (
	"bytes"
	"os/exec"
	"strings"
	"testing"
)

func TestMainProgram(t *testing.T) {
	// Test the example sequence from the problem description
	input := "189\n113\n121\n114\n145\n110\n145\n"
	
	cmd := exec.Command("go", "run", "../main.go")
	cmd.Stdin = strings.NewReader(input)
	
	var out bytes.Buffer
	cmd.Stdout = &out
	
	err := cmd.Run()
	if err != nil {
		t.<PERSON><PERSON>("Failed to run main program: %v", err)
	}
	
	output := out.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	
	// Should have 7 lines of output (one for each input number)
	if len(lines) != 7 {
		t.<PERSON><PERSON>("Expected 7 lines of output, got %d", len(lines))
		t.Logf("Output: %s", output)
		return
	}
	
	// Each line should contain two numbers separated by space
	for i, line := range lines {
		parts := strings.Fields(line)
		if len(parts) != 2 {
			t.<PERSON><PERSON><PERSON>("Line %d: expected 2 numbers, got %d: %s", i+1, len(parts), line)
		}
	}
}

func TestSequentialPredictions(t *testing.T) {
	// Test that predictions are reasonable for known sequences
	testCases := []struct {
		name     string
		sequence []string
		validate func(outputs []string) bool
	}{
		{
			name:     "simple increasing",
			sequence: []string{"1", "2", "3", "4", "5"},
			validate: func(outputs []string) bool {
				// Each prediction should be reasonable
				return len(outputs) == 5
			},
		},
		{
			name:     "simple decreasing", 
			sequence: []string{"10", "9", "8", "7", "6"},
			validate: func(outputs []string) bool {
				return len(outputs) == 5
			},
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			input := strings.Join(tc.sequence, "\n") + "\n"
			
			cmd := exec.Command("go", "run", "../main.go")
			cmd.Stdin = strings.NewReader(input)
			
			var out bytes.Buffer
			cmd.Stdout = &out
			
			err := cmd.Run()
			if err != nil {
				t.Fatalf("Failed to run main program: %v", err)
			}
			
			output := out.String()
			lines := strings.Split(strings.TrimSpace(output), "\n")
			
			if !tc.validate(lines) {
				t.Errorf("Validation failed for sequence %v, output: %s", tc.sequence, output)
			}
		})
	}
}

func TestInvalidInput(t *testing.T) {
	// Test that program handles invalid input gracefully
	input := "189\ninvalid\n113\nabc\n121\n"
	
	cmd := exec.Command("go", "run", "../main.go")
	cmd.Stdin = strings.NewReader(input)
	
	var out bytes.Buffer
	cmd.Stdout = &out
	
	err := cmd.Run()
	if err != nil {
		t.Fatalf("Program should handle invalid input gracefully: %v", err)
	}
	
	output := out.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")
	
	// Should have 3 lines (for 189, 113, 121 - invalid inputs skipped)
	if len(lines) != 3 {
		t.Errorf("Expected 3 lines of output (invalid inputs skipped), got %d", len(lines))
		t.Logf("Output: %s", output)
	}
}

func TestEmptyInput(t *testing.T) {
	// Test program with no input
	cmd := exec.Command("go", "run", "../main.go")
	cmd.Stdin = strings.NewReader("")
	
	var out bytes.Buffer
	cmd.Stdout = &out
	
	err := cmd.Run()
	if err != nil {
		t.Fatalf("Program should handle empty input gracefully: %v", err)
	}
	
	output := out.String()
	if output != "" {
		t.Errorf("Expected no output for empty input, got: %s", output)
	}
}
