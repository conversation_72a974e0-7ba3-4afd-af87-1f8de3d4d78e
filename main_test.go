package main

import (
	"math"
	"testing"
)

func TestNumberPredictor_AddNumber(t *testing.T) {
	predictor := NewNumberPredictor()
	
	predictor.AddNumber(1.0)
	if len(predictor.numbers) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 number, got %d", len(predictor.numbers))
	}
	
	predictor.AddNumber(2.0)
	if len(predictor.numbers) != 2 {
		t.<PERSON><PERSON>("Expected 2 numbers, got %d", len(predictor.numbers))
	}
	
	if len(predictor.differences) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 difference, got %d", len(predictor.differences))
	}
	
	if predictor.differences[0] != 1.0 {
		t.<PERSON><PERSON><PERSON>("Expected difference of 1.0, got %f", predictor.differences[0])
	}
}

func TestMean(t *testing.T) {
	numbers := []float64{1, 2, 3, 4, 5}
	result := mean(numbers)
	expected := 3.0
	
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Expected mean %f, got %f", expected, result)
	}
	
	// Test empty slice
	empty := []float64{}
	result = mean(empty)
	if result != 0 {
		t.<PERSON><PERSON><PERSON>("Expected mean of empty slice to be 0, got %f", result)
	}
}

func TestStandardDeviation(t *testing.T) {
	numbers := []float64{1, 2, 3, 4, 5}
	result := standardDeviation(numbers)
	
	// Expected standard deviation for this sequence is approximately 1.58
	expected := 1.5811388300841898
	
	if math.Abs(result-expected) > 0.0001 {
		t.Errorf("Expected standard deviation %f, got %f", expected, result)
	}
	
	// Test with less than 2 numbers
	single := []float64{1}
	result = standardDeviation(single)
	if result != 0 {
		t.Errorf("Expected standard deviation of single number to be 0, got %f", result)
	}
}

func TestAnalyzeTrend(t *testing.T) {
	predictor := NewNumberPredictor()
	
	// Test increasing trend
	predictor.AddNumber(1)
	predictor.AddNumber(2)
	predictor.AddNumber(3)
	predictor.AddNumber(4)
	
	trend, avgDiff, _ := predictor.AnalyzeTrend()
	
	if trend != "increasing" {
		t.Errorf("Expected increasing trend, got %s", trend)
	}
	
	if avgDiff != 1.0 {
		t.Errorf("Expected average difference of 1.0, got %f", avgDiff)
	}
	
	// Test decreasing trend
	predictor2 := NewNumberPredictor()
	predictor2.AddNumber(10)
	predictor2.AddNumber(8)
	predictor2.AddNumber(6)
	
	trend2, avgDiff2, _ := predictor2.AnalyzeTrend()
	
	if trend2 != "decreasing" {
		t.Errorf("Expected decreasing trend, got %s", trend2)
	}
	
	if avgDiff2 != -2.0 {
		t.Errorf("Expected average difference of -2.0, got %f", avgDiff2)
	}
}

func TestPredictRange(t *testing.T) {
	predictor := NewNumberPredictor()
	
	// Test with no numbers
	min, max := predictor.PredictRange(2.0)
	if min != 0 || max != 100 {
		t.Errorf("Expected default range [0, 100], got [%f, %f]", min, max)
	}
	
	// Test with one number
	predictor.AddNumber(50)
	min, max = predictor.PredictRange(2.0)
	if min != 0 || max != 100 {
		t.Errorf("Expected range [0, 100] for single number, got [%f, %f]", min, max)
	}
	
	// Test with increasing sequence
	predictor2 := NewNumberPredictor()
	predictor2.AddNumber(1)
	predictor2.AddNumber(2)
	predictor2.AddNumber(3)
	
	min2, max2 := predictor2.PredictRange(2.0)
	
	// Should predict around 4 (3 + 1) with some range
	if min2 > 4 || max2 < 4 {
		t.Errorf("Expected range to contain 4, got [%f, %f]", min2, max2)
	}
	
	// Range should be reasonable (not too wide or too narrow)
	rangeWidth := max2 - min2
	if rangeWidth < 1 || rangeWidth > 20 {
		t.Errorf("Expected reasonable range width, got %f", rangeWidth)
	}
}

func TestMinMax(t *testing.T) {
	numbers := []float64{3, 1, 4, 1, 5, 9, 2, 6}
	
	minVal := min(numbers)
	maxVal := max(numbers)
	
	if minVal != 1 {
		t.Errorf("Expected min 1, got %f", minVal)
	}
	
	if maxVal != 9 {
		t.Errorf("Expected max 9, got %f", maxVal)
	}
	
	// Test empty slice
	empty := []float64{}
	minEmpty := min(empty)
	maxEmpty := max(empty)
	
	if minEmpty != 0 || maxEmpty != 0 {
		t.Errorf("Expected min/max of empty slice to be 0, got %f/%f", minEmpty, maxEmpty)
	}
}

func TestGetStats(t *testing.T) {
	predictor := NewNumberPredictor()
	predictor.AddNumber(1)
	predictor.AddNumber(2)
	predictor.AddNumber(3)
	
	stats := predictor.GetStats()
	
	if stats["count"] != 3 {
		t.Errorf("Expected count 3, got %v", stats["count"])
	}
	
	if stats["mean"] != 2.0 {
		t.Errorf("Expected mean 2.0, got %v", stats["mean"])
	}
	
	if stats["trend"] != "increasing" {
		t.Errorf("Expected increasing trend, got %v", stats["trend"])
	}
	
	if stats["last_number"] != 3.0 {
		t.Errorf("Expected last number 3.0, got %v", stats["last_number"])
	}
}
