package main

import (
	"fmt"
	"strings"
)

// Example demonstrates how the NumberPredictor works
func runExample() {
	fmt.Println("=== Number Prediction Example ===")
	
	predictor := NewNumberPredictor()
	
	// Test with different sequences
	sequences := map[string][]float64{
		"Linear increasing": {1, 2, 3, 4, 5},
		"Linear decreasing": {20, 18, 16, 14, 12},
		"Random-like":       {5, 12, 8, 15, 3, 18},
		"Quadratic":         {1, 4, 9, 16, 25},
		"Oscillating":       {10, 5, 15, 3, 20, 1},
	}
	
	for name, sequence := range sequences {
		fmt.Printf("\n%s: %v\n", name, sequence)
		fmt.Println(strings.Repeat("-", 40))
		
		predictor := NewNumberPredictor()
		
		for i, num := range sequence {
			predictor.AddNumber(num)
			
			if i > 0 { // Need at least 2 numbers for meaningful prediction
				min, max := predictor.PredictRange(2.0)
				fmt.Printf("After %v: predicted range [%.2f, %.2f]\n", 
					sequence[:i+1], min, max)
				
				// Show if next number (if exists) falls in predicted range
				if i < len(sequence)-1 {
					next := sequence[i+1]
					inRange := min <= next && next <= max
					status := "✗"
					if inRange {
						status = "✓"
					}
					fmt.Printf("  Next actual: %.2f %s\n", next, status)
				}
			}
		}
		
		// Show final statistics
		stats := predictor.GetStats()
		fmt.Printf("Final stats: trend=%s, avg_diff=%.2f, std_dev=%.2f\n",
			stats["trend"], stats["avg_diff"], stats["std_dev"])
	}
}

// SimulateCommandLine shows how the program works from command line
func simulateCommandLine() {
	fmt.Println("\n=== Command Line Simulation ===")
	fmt.Println("Input sequence: 10, 12, 15, 18, 22")
	fmt.Println("Expected output:")
	
	predictor := NewNumberPredictor()
	inputs := []float64{10, 12, 15, 18, 22}
	
	for _, input := range inputs {
		predictor.AddNumber(input)
		min, max := predictor.PredictRange(2.0)
		fmt.Printf("Input: %.0f -> Output: %.2f %.2f\n", input, min, max)
	}
}

// BenchmarkExample shows performance characteristics
func benchmarkExample() {
	fmt.Println("\n=== Performance Example ===")
	
	predictor := NewNumberPredictor()
	
	// Add many numbers to test performance
	fmt.Println("Adding 1000 numbers...")
	for i := 0; i < 1000; i++ {
		// Create a somewhat realistic sequence
		value := float64(i) + float64(i%10)*0.1
		predictor.AddNumber(value)
	}
	
	// Get final prediction
	min, max := predictor.PredictRange(2.0)
	stats := predictor.GetStats()
	
	fmt.Printf("Final prediction after 1000 numbers: [%.2f, %.2f]\n", min, max)
	fmt.Printf("Sequence stats: mean=%.2f, std_dev=%.2f, trend=%s\n",
		stats["mean"], stats["std_dev"], stats["trend"])
}

func main() {
	runExample()
	simulateCommandLine()
	benchmarkExample()
	
	fmt.Println("\n=== Usage Instructions ===")
	fmt.Println("To use the main program:")
	fmt.Println("1. Run: go run main.go")
	fmt.Println("2. Enter numbers one per line")
	fmt.Println("3. Program outputs predicted range for next number")
	fmt.Println("4. Type 'quit' to exit")
	fmt.Println("\nExample:")
	fmt.Println("$ echo -e '10\\n12\\n15\\n18\\nquit' | go run main.go")
}
